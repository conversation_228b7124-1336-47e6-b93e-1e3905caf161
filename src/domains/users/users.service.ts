import { Injectable, NotFoundException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { User } from './user.schema';
import { AlertZoneService } from '../alertZones/alertZone.service';
import { CacheKeyPatterns } from '../../utils/cache.utils';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<User>,
    private readonly alertZoneService: AlertZoneService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  async findAll(filter: any): Promise<User[]> {
    return this.userModel.find(filter).exec();
  }

  async findOne(find: any): Promise<User> {
    // Cache user lookups by 'sub' (most common in authentication)
    if (find.sub) {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(find.sub);

      // Try to get from cache first
      const cachedUser = await this.cacheManager.get<User>(cacheKey);
      if (cachedUser && process.env.ENABLE_CACHE === 'true') {
        return cachedUser;
      }

      // If not in cache, query database
      const user = await this.userModel.findOne(find).exec();

      // Store in cache with 5 minute TTL (300 seconds)
      if (user && process.env.ENABLE_CACHE === 'true') {
        await this.cacheManager.set(cacheKey, user, 300000); // 5 minutes in milliseconds
      }

      return user;
    }

    // For other queries, don't cache
    return this.userModel.findOne(find).exec();
  }

  async findOrCreateUser(auth0Info: any): Promise<any> {
    const {
      sub,
      given_name,
      family_name,
      nickname,
      name,
      picture,
      updated_at,
      created_at,
      email,
      email_verified,
      service_zones,
      org_id,
      orgs,
      phone_number,
      phone_verified,
      roles,
      last_activity,
      org_name
    } = auth0Info;

    const user = await this.userModel
      .findOneAndUpdate(
        { sub },
        {
          sub,
          given_name,
          family_name,
          nickname,
          name,
          picture,
          updated_at,
          created_at,
          email,
          email_verified,
          service_zones,
          org_id,
          orgs,
          phone_number,
          phone_verified,
          roles,
          last_activity,
        },
        { new: true, upsert: true },
      )
      .exec();

    // Invalidate cache for this user
    if(process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(sub);
      await this.cacheManager.del(cacheKey);
    }

    const userAlertZone = this.alertZoneService.findZonesForUser(user['_id'].toString());
    const orgAlertZone = this.alertZoneService.findZonesForOrganization(user['org_id']);

    const [userAlertZoneF, orgAlertZoneF] = await Promise.all([userAlertZone, orgAlertZone]);

    user['alertZones'] = userAlertZone;
    user['org_name'] = org_name;
    return { ...user['_doc'], alertZones: userAlertZoneF, orgAlertZones: orgAlertZoneF };
  }

  async assignServiceZone(userId: String, assignServiceZoneReq: any) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Add the service zone if it doesn't already exist
    if (!user.service_zones.includes(assignServiceZoneReq['serviceZoneIds'])) {
      assignServiceZoneReq['serviceZoneIds'].map((serviceZoneId) => {
        const zoneObjectId = new Types.ObjectId(serviceZoneId);
        user.service_zones.push(zoneObjectId);
      });
    }

    const savedUser = await user.save();

    // Invalidate cache for this user
    if (user.sub && process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(user.sub);
      await this.cacheManager.del(cacheKey);
    }

    return savedUser;
  }

  async removeUserBySub(sub: string) {
    const query = { sub };
    const result = await this.userModel.deleteOne(query);

    // Invalidate cache for this user
    if(process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(sub);
      await this.cacheManager.del(cacheKey);
    }

    if (result.deletedCount === 1) {
      console.log("Successfully deleted one document.");
    } else {
      console.log("No documents matched the query. Deleted 0 documents.");
    }
    return result;
  }

}
