import { Controller, Get, Query, Req } from '@nestjs/common';
import { DashboardService } from './dashboard.service';

@Controller('api/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('/stats')
  async getDashboardStats(
    @Req() req: Request,
    @Query('alertZoneIds') alertZoneIds?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('uasIds') uasIds?: string,
  ) {
    const orgId = req['org_id'];

    let alertZoneIdsArray: string[] | undefined;
    let uasIdsArray: string[] | undefined;
    if (alertZoneIds) alertZoneIdsArray = alertZoneIds.split(',').map(id => id.trim());
    if (uasIds) uasIdsArray = uasIds.split(',').map(id => id.trim());
    
    return this.dashboardService.getDashboardStats(orgId, alertZoneIdsArray, startDate, endDate, uasIdsArray);
  }
}
