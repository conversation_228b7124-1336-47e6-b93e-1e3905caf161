ENV=dev
PORT=3000
MSK_BROKERS='["b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198"]'

MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev"
DATABASE_NAME=coddn

AWS_ACCESS_KEY_ID=********************
AWS_SERCRET_KEY_ID=XB5g7U5NBtDCzd5vTXoJLumcDEuzA8sOulVO2tIg
AWS_REGION=us-east-2

MSK_PYTHON_BROKERS='b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'

AUTH0_ISSUER_URL=https://airwarden-essentials-dev.us.auth0.com
AUTH0_AUDIENCE=https://api-dev.dronegohome.tech
AUTH0_CLIENT_ID=DBm4C7RY8cr56yTEibLkCQk6c2m2y54B
AUTH0_SECRET_KEY=****************************************************************

AUTH0_MANAGEMENT_API=https://airwarden-essentials-dev.us.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=x45Rst0K3nbLnm7fwyVxHtgkFVcJG4ar
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************

GROUP_ID=devGroupApi

RID_UI_TOPIC="DETECTION_DEV"
RID_TOPIC="RID_DEV"

APP_SYNC_URL='https://tmeo67xkzzccncxp3yywd2vlle.appsync-api.us-east-2.amazonaws.com/graphql'
APP_SYNC_API_KEY='da2-ll3y5q2wafhqzgzu7b2dj2wx2m'

REDIS_HOST="rediss://valkey-connect:<EMAIL>:6379"

ENABLE_CACHE=false